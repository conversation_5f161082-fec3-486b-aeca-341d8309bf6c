import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:pinput/pinput.dart';
import '../../../core/config/environment_config.dart';
import '../../../core/utils/constants.dart';
import '../../../core/utils/validators.dart';
import '../../../routes/app_routes.dart';
import '../../../shared/widgets/primary_button.dart';
import '../../../shared/widgets/modern_gradient_container.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';

/// OTP verification screen
class OtpVerificationScreen extends StatefulWidget {
  final String phoneNumber;

  const OtpVerificationScreen({super.key, required this.phoneNumber});

  @override
  State<OtpVerificationScreen> createState() => _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends State<OtpVerificationScreen> {
  final _otpController = TextEditingController();
  bool _isLoading = false;
  int _resendCooldown = 30;
  Timer? _resendTimer;

  @override
  void initState() {
    super.initState();
    _startResendTimer();
    // Log the phone number for debugging
    debugPrint('OTP Screen initialized with phone: ${widget.phoneNumber}');
  }

  @override
  void dispose() {
    _otpController.dispose();
    _resendTimer?.cancel();
    super.dispose();
  }

  void _startResendTimer() {
    _resendTimer?.cancel();
    setState(() => _resendCooldown = 30);

    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _resendCooldown--;
          if (_resendCooldown <= 0) {
            timer.cancel();
          }
        });
      }
    });
  }

  void _handleVerifyOtp() {
    final otp = _otpController.text;
    if (otp.length == EnvironmentConfig.otpLength) {
      // Make sure we have a valid phone number
      final phoneNumber = widget.phoneNumber.trim();
      if (phoneNumber.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Phone number is missing. Please go back and try again.',
            ),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      context.read<AuthBloc>().add(
        AuthEvent.verifyOtp(phoneNumber: phoneNumber, otp: otp),
      );
    }
  }

  void _handleResendOtp() {
    if (_resendCooldown <= 0) {
      context.read<AuthBloc>().add(const AuthEvent.resendOtp());
      _startResendTimer();
    }
  }

  @override
  Widget build(BuildContext context) {
    final defaultPinTheme = PinTheme(
      width: 50.w,
      height: 56.h,
      textStyle: Theme.of(
        context,
      ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.w600),
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
        ),
        borderRadius: BorderRadius.circular(12.r),
      ),
    );

    final focusedPinTheme = defaultPinTheme.copyDecorationWith(
      border: Border.all(
        color: Theme.of(context).colorScheme.primary,
        width: 2,
      ),
    );

    final submittedPinTheme = defaultPinTheme.copyDecorationWith(
      color: Theme.of(context).colorScheme.primaryContainer,
      border: Border.all(color: Theme.of(context).colorScheme.primary),
    );

    final errorPinTheme = defaultPinTheme.copyDecorationWith(
      border: Border.all(color: Theme.of(context).colorScheme.error),
    );
    // Format phone number for display
    final formattedPhone = widget.phoneNumber.isEmpty
        ? 'Unknown'
        : widget.phoneNumber;
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: Text('Verification', style: TextStyle(fontSize: 28.sp)),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      extendBodyBehindAppBar: true,
      body: BlocConsumer<AuthBloc, AuthState>(
        listener: (context, state) {
          state.maybeWhen(
            otpSent: (phoneNumber, resendCooldown) {
              setState(() {
                _resendCooldown = resendCooldown;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('OTP sent successfully'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            verifyingOtp: () {
              setState(() {
                _isLoading = true;
              });
            },
            authenticated: (user, isNewUser) {
              setState(() {
                _isLoading = false;
              });
              context.go(AppRoutes.home);
            },
            profileIncomplete: (user) {
              setState(() {
                _isLoading = false;
              });
              context.go(AppRoutes.profileSetup);
            },
            error: (message, phoneNumber) {
              setState(() {
                _isLoading = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(message),
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              );
            },
            loading: () {
              setState(() {
                _isLoading = true;
              });
            },
            orElse: () {
              setState(() {
                _isLoading = false;
              });
            },
          );
        },
        builder: (context, state) {
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Theme.of(context).colorScheme.surface,
                  Theme.of(
                    context,
                  ).colorScheme.primaryContainer.withValues(alpha: 0.1),
                ],
              ),
            ),
            child: SafeArea(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Container(
                    constraints: BoxConstraints(
                      minHeight:
                          MediaQuery.of(context).size.height -
                          MediaQuery.of(context).padding.top -
                          kToolbarHeight,
                    ),
                    child: Column(
                      children: [
                        SizedBox(height: 30.h),

                        // Header Section
                        _buildHeaderSection(context),

                        SizedBox(height: 30.h),

                        // OTP Input Section
                        _buildOtpInputSection(
                          context,
                          defaultPinTheme,
                          focusedPinTheme,
                          submittedPinTheme,
                          errorPinTheme,
                        ),

                        SizedBox(height: 22.h),

                        // Resend Section
                        _buildResendSection(context),

                        SizedBox(height: 35.h),

                        // Verify Button
                        PrimaryButton(
                          text: 'Verify',
                          onPressed: _isLoading ? null : _handleVerifyOtp,
                          isLoading: _isLoading,
                        ),

                        SizedBox(height: 30.h),

                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context) {
    final formattedPhone = widget.phoneNumber.isEmpty
        ? 'Unknown'
        : widget.phoneNumber;

    return ModernGradientContainer(
      padding: EdgeInsets.all(20.w),
      child: Column(
        children: [
          Container(
            width: 80.w,
            height: 80.h,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 2,
              ),
            ),
            child: Icon(Icons.lock_outline, size: 50.sp, color: Colors.white),
          ),
          SizedBox(height: 24.h),
          Text(
            'Enter OTP',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          SizedBox(height: 12.h),
          Text(
            'We\'ve sent a 6-digit code to',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            '+91 $formattedPhone',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOtpInputSection(
    BuildContext context,
    PinTheme defaultPinTheme,
    PinTheme focusedPinTheme,
    PinTheme submittedPinTheme,
    PinTheme errorPinTheme,
  ) {
    return GlassMorphismContainer(
      padding: EdgeInsets.all(24.w),
      // decoration: BoxDecoration(
      //   color: Theme.of(context).colorScheme.surface,
      //   borderRadius: BorderRadius.circular(20.r),
      //   boxShadow: [
      //     BoxShadow(
      //       color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
      //       blurRadius: 15,
      //       offset: const Offset(0, 5),
      //     ),
      //   ],
      // ),
      child: Column(
        children: [
          Text(
            'Enter Verification Code',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
          ),
          SizedBox(height: 24.h),
          Pinput(
            controller: _otpController,
            length: EnvironmentConfig.otpLength,
            defaultPinTheme: _buildModernPinTheme(context),
            focusedPinTheme: _buildFocusedPinTheme(context),
            submittedPinTheme: _buildSubmittedPinTheme(context),
            errorPinTheme: _buildErrorPinTheme(context),
            showCursor: true,
            enabled: !_isLoading,
            onCompleted: (_) => _handleVerifyOtp(),
            validator: (value) => Validators.validateOTP(value),
          ),
        ],
      ),
    );
  }

  Widget _buildResendSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: Theme.of(
          context,
        ).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.timer_outlined,
            size: 20.sp,
            color: _resendCooldown > 0
                ? Theme.of(context).colorScheme.onSurfaceVariant
                : Theme.of(context).colorScheme.primary,
          ),
          SizedBox(width: 8.w),
          TextButton(
            onPressed: _resendCooldown <= 0 && !_isLoading
                ? _handleResendOtp
                : null,
            child: Text(
              _resendCooldown > 0
                  ? 'Resend OTP in $_resendCooldown seconds'
                  : 'Resend OTP',
              style: TextStyle(
                color: _resendCooldown > 0
                    ? Theme.of(context).colorScheme.onSurfaceVariant
                    : Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  PinTheme _buildModernPinTheme(BuildContext context) {
    return PinTheme(
      width: 56.w,
      height: 64.h,
      textStyle: Theme.of(
        context,
      ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.w600),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          width: 2,
        ),
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
    );
  }

  PinTheme _buildFocusedPinTheme(BuildContext context) {
    return _buildModernPinTheme(context).copyDecorationWith(
      border: Border.all(
        color: Theme.of(context).colorScheme.primary,
        width: 2.5,
      ),
      boxShadow: [
        BoxShadow(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
          blurRadius: 12,
          offset: const Offset(0, 4),
        ),
      ],
    );
  }

  PinTheme _buildSubmittedPinTheme(BuildContext context) {
    return _buildModernPinTheme(context).copyDecorationWith(
      color: Theme.of(context).colorScheme.primaryContainer,
      border: Border.all(
        color: Theme.of(context).colorScheme.primary,
        width: 2,
      ),
    );
  }

  PinTheme _buildErrorPinTheme(BuildContext context) {
    return _buildModernPinTheme(context).copyDecorationWith(
      border: Border.all(color: Theme.of(context).colorScheme.error, width: 2),
    );
  }
}
