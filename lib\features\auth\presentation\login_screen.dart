import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import '../../../core/utils/constants.dart';
import '../../../core/utils/validators.dart';
import '../../../shared/widgets/primary_button.dart';
import '../../../shared/widgets/modern_gradient_container.dart';
import '../../../shared/widgets/modern_input_field.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../../../routes/app_routes.dart';
import '../bloc/auth_state.dart';

/// Login screen with phone number input
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  void _handleSendOtp() {
    if (_formKey.currentState?.validate() ?? false) {
      // Remove any formatting from phone number
      final phoneNumber = _phoneController.text.replaceAll(RegExp(r'\D'), '');

      context.read<AuthBloc>().add(AuthEvent.sendOtp(phoneNumber: phoneNumber));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[200],
      body: BlocConsumer<AuthBloc, AuthState>(
        listener: (context, state) {
          state.maybeWhen(
            otpSending: () {
              setState(() {
                _isLoading = true;
              });
            },
            otpSent: (phoneNumber, resendCooldown) {
              setState(() {
                _isLoading = false;
              });
              // Make sure we're passing the phone number correctly
              final phone = phoneNumber.trim();
              debugPrint('Navigating to OTP screen with phone: $phone');
              context.go(
                '${AppRoutes.otpVerification}?phone=$phone',
                extra: phone,
              );
            },
            error: (message, phoneNumber) {
              setState(() {
                _isLoading = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(message),
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              );
            },
            orElse: () {
              setState(() {
                _isLoading = false;
              });
            },
          );
        },
        builder: (context, state) {
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Theme.of(context).colorScheme.surface,
                  Theme.of(
                    context,
                  ).colorScheme.primaryContainer.withValues(alpha: 0.1),
                ],
              ),
            ),
            child: SafeArea(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Form(
                    key: _formKey,
                    child: Container(
                      constraints: BoxConstraints(
                        minHeight:
                            MediaQuery.of(context).size.height -
                            MediaQuery.of(context).padding.top,
                      ),
                      child: Column(
                        children: [
                          SizedBox(height: 30.h),

                          // Logo and Title Section
                          _buildHeaderSection(context),

                          SizedBox(height: 30.h),

                          // Welcome Section
                          _buildWelcomeSection(context),

                          SizedBox(height: 20.h),

                          // Phone Input Section
                          _buildPhoneInputSection(context),

                          SizedBox(height: 32.h),

                          // Send OTP Button
                          PrimaryButton(
                            text: 'Send OTP',
                            onPressed: _isLoading ? null : _handleSendOtp,
                            isLoading: _isLoading,
                          ),

                          SizedBox(height: 40.h),

                          // Terms and Privacy
                          _buildTermsSection(context),

                          SizedBox(height: 20.h),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.secondary,
          ],
        ),
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        children: [
          Container(
            width: 100.w,
            height: 100.h,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 2,
              ),
            ),
            child: Icon(
              Icons.shield_outlined,
              size: 60.sp,
              color: Colors.white,
            ),
          ),
          SizedBox(height: 24.h),
          Text(
            AppConstants.appName,
            style: Theme.of(context).textTheme.headlineLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.white,
              letterSpacing: 2.0,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            AppConstants.appFullName,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            AppConstants.appTagline,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontStyle: FontStyle.italic,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    return GlassMorphismContainer(
      padding: EdgeInsets.all(24.w),
      child: Column(
        children: [
          Text(
            'Welcome!',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          SizedBox(height: 12.h),
          Text(
            'Enter your phone number to get started',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPhoneInputSection(BuildContext context) {
    return ModernInputField(
      controller: _phoneController,
      labelText: 'Phone Number',
      hintText: 'Enter phone number',
      prefixIcon: Icons.phone_outlined,
      prefixText: '+91 ',
      keyboardType: TextInputType.phone,
      maxLength: 10,
      enabled: !_isLoading,
      autofillHints: const [AutofillHints.telephoneNumberLocal],
      textInputAction: TextInputAction.done,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(10),
      ],
      onChanged: (value) {
        if (value.length == 10) {
          _handleSendOtp();
        }
      },
      validator: Validators.validatePhoneNumber,
      onFieldSubmitted: _handleSendOtp,
    );
  }

  Widget _buildTermsSection(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Text.rich(
        TextSpan(
          text: 'By continuing, you agree to our\n',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          children: [
            TextSpan(
              text: 'Terms of Service',
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w600,
                decoration: TextDecoration.underline,
              ),
            ),
            const TextSpan(text: ' and '),
            TextSpan(
              text: 'Privacy Policy',
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w600,
                decoration: TextDecoration.underline,
              ),
            ),
          ],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
